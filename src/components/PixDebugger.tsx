import React, { useState } from 'react';
import { testSantanderPixPayload, analyzePixPayload, decodePixPayload, validatePixPayload } from '../utils/pix';

export function PixDebugger() {
  const [payload, setPayload] = useState('');
  const [result, setResult] = useState<any>(null);

  const handleTestSantander = () => {
    console.clear();
    testSantanderPixPayload();
    setResult('Teste executado - verifique o console');
  };

  const handleAnalyzePayload = () => {
    if (!payload.trim()) {
      setResult('Digite um payload PIX para analisar');
      return;
    }

    try {
      const decoded = decodePixPayload(payload);
      const validation = validatePixPayload(payload);
      
      setResult({
        payload: payload,
        decoded: decoded,
        validation: validation
      });

      console.log('=== ANÁLISE DO PAYLOAD ===');
      console.log('Payload:', payload);
      console.log('Decodificado:', decoded);
      console.log('Validação:', validation);
    } catch (error) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50">
      <h3 className="font-bold text-gray-900 mb-3">PIX Debugger</h3>
      
      <div className="space-y-3">
        <button
          onClick={handleTestSantander}
          className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
        >
          Testar Santander
        </button>

        <div>
          <textarea
            value={payload}
            onChange={(e) => setPayload(e.target.value)}
            placeholder="Cole um payload PIX aqui para analisar..."
            className="w-full px-2 py-1 border border-gray-300 rounded text-xs"
            rows={3}
          />
          <button
            onClick={handleAnalyzePayload}
            className="w-full mt-1 px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
          >
            Analisar Payload
          </button>
        </div>

        {result && (
          <div className="bg-gray-100 p-2 rounded text-xs max-h-40 overflow-y-auto">
            <pre>{typeof result === 'string' ? result : JSON.stringify(result, null, 2)}</pre>
          </div>
        )}
      </div>
    </div>
  );
}
