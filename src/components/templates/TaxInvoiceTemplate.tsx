import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function TaxInvoiceTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-6 font-mono text-sm border border-black">
      {/* Header with TAX INVOICE title */}
      <div className="border-b-2 border-black pb-2 mb-4">
        <div className="text-center">
          <div className="border border-black inline-block px-8 py-2 mb-4">
            <h1 className="text-lg font-bold">TAX INVOICE</h1>
          </div>
        </div>
        
        {/* Company Name and Address */}
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold mb-2">{data.company.name}</h2>
          <p className="text-sm">
            {data.company.address}, {data.company.city}, {data.company.state}, {data.company.zipCode}
          </p>
        </div>
      </div>

      {/* Invoice Details Grid */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-xs">
        <div>
          <div className="mb-2">
            <span className="font-bold">GSTIN:</span> {data.company.cnpj || 'N/A'}
          </div>
          <div className="mb-2">
            <span className="font-bold">PAN:</span> {data.company.cnpj ? data.company.cnpj.substring(0, 10) : 'N/A'}
          </div>
          <div className="mb-2">
            <span className="font-bold">State Code:</span> {data.company.state.substring(0, 2).toUpperCase()}
          </div>
        </div>
        <div className="text-right">
          <div className="mb-2">
            <span className="font-bold">Invoice No.:</span> {data.details.number}
          </div>
          <div className="mb-2">
            <span className="font-bold">Invoice Date:</span> {formatDate(data.details.issueDate)}
          </div>
        </div>
      </div>

      {/* Recipient/Billed To and Consignee/Shipped To */}
      <div className="grid grid-cols-2 gap-4 mb-4 border-t border-b border-black py-2">
        <div>
          <div className="mb-3">
            <h3 className="font-bold text-xs mb-1">Recipient/Billed to</h3>
            <div className="text-xs">
              <p className="font-bold">{data.client.name}</p>
              <p>{data.client.address}</p>
              <p>{data.client.city}, {data.client.state} {data.client.zipCode}</p>
              {data.client.cpfCnpj && <p>GSTIN: {data.client.cpfCnpj}</p>}
            </div>
          </div>
          <div className="text-xs">
            <p><span className="font-bold">GSTIN:</span> {data.client.cpfCnpj || 'N/A'}</p>
            <p><span className="font-bold">State Code:</span> {data.client.state.substring(0, 2).toUpperCase()}</p>
          </div>
        </div>
        
        <div>
          <div className="mb-3">
            <h3 className="font-bold text-xs mb-1">Consignee/Shipped to</h3>
            <div className="text-xs">
              <p className="font-bold">{data.shipping.address ? data.client.name : data.client.name}</p>
              <p>{data.shipping.address || data.client.address}</p>
              <p>{data.shipping.city || data.client.city}, {data.shipping.state || data.client.state} {data.shipping.zipCode || data.client.zipCode}</p>
            </div>
          </div>
          <div className="text-xs">
            <p><span className="font-bold">Material to be delivered at:</span></p>
            <p>{data.shipping.address || data.client.address}</p>
          </div>
        </div>
      </div>

      {/* Place of Supply and Order Details */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-xs">
        <div>
          <p><span className="font-bold">Place of Supply:</span> {data.client.state}</p>
        </div>
        <div className="text-right">
          <p><span className="font-bold">Order No./Date:</span> {data.details.number} / {formatDate(data.details.issueDate)}</p>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-4">
        <table className="w-full border-collapse border border-black text-xs">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-black p-2 text-left font-bold">S.No</th>
              <th className="border border-black p-2 text-left font-bold">Description & Specification of Goods</th>
              <th className="border border-black p-2 text-center font-bold">HSN Code</th>
              <th className="border border-black p-2 text-center font-bold">No of PCS</th>
              <th className="border border-black p-2 text-center font-bold">Quantity</th>
              <th className="border border-black p-2 text-center font-bold">UOM</th>
              <th className="border border-black p-2 text-right font-bold">Rate/Unit</th>
              <th className="border border-black p-2 text-center font-bold">Disc. (%)</th>
              <th className="border border-black p-2 text-right font-bold">Value ₹</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id}>
                <td className="border border-black p-2 text-center">{index + 1}</td>
                <td className="border border-black p-2">{item.description}</td>
                <td className="border border-black p-2 text-center">84186090</td>
                <td className="border border-black p-2 text-center">{formatNumber(item.quantity)}</td>
                <td className="border border-black p-2 text-center">{formatNumber(item.quantity)}</td>
                <td className="border border-black p-2 text-center">BUNDLE</td>
                <td className="border border-black p-2 text-right">{formatCurrency(item.unitPrice)}</td>
                <td className="border border-black p-2 text-center">-</td>
                <td className="border border-black p-2 text-right">{formatCurrency(item.total)}</td>
              </tr>
            ))}
            {/* Empty rows to match the original layout */}
            {Array.from({ length: Math.max(0, 8 - data.items.length) }).map((_, index) => (
              <tr key={`empty-${index}`}>
                <td className="border border-black p-2 h-8">&nbsp;</td>
                <td className="border border-black p-2">&nbsp;</td>
                <td className="border border-black p-2">&nbsp;</td>
                <td className="border border-black p-2">&nbsp;</td>
                <td className="border border-black p-2">&nbsp;</td>
                <td className="border border-black p-2">&nbsp;</td>
                <td className="border border-black p-2">&nbsp;</td>
                <td className="border border-black p-2">&nbsp;</td>
                <td className="border border-black p-2">&nbsp;</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Grand Total Row */}
      <div className="mb-4">
        <table className="w-full border-collapse border border-black text-xs">
          <tr className="bg-gray-100">
            <td className="border border-black p-2 text-right font-bold" colSpan={7}>Grand Total :</td>
            <td className="border border-black p-2 text-center font-bold">Ass. Value</td>
            <td className="border border-black p-2 text-right font-bold">{formatCurrency(data.subtotal)}</td>
          </tr>
        </table>
      </div>

      {/* Bank Details and QR Code Section */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="border border-black p-3">
            <div className="flex justify-between">
              <div>
                <h4 className="font-bold text-xs mb-2">Our Bank Details :</h4>
                <div className="text-xs space-y-1">
                  <p><span className="font-bold">Bank Name:</span> AXIS BANK LTD</p>
                  <p><span className="font-bold">Account Name:</span> {data.company.name}</p>
                  <p><span className="font-bold">Account No.:</span> ***************</p>
                  <p><span className="font-bold">IFSC Code:</span> UTIB0004626</p>
                  <p><span className="font-bold">UPI:</span> {data.pix.key}</p>
                </div>
              </div>
              <div className="text-center ml-4">
                <h4 className="font-bold text-xs mb-2">E-Invoice</h4>
                {qrCodeUrl && (
                  <img src={qrCodeUrl} alt="QR Code" className="w-24 h-24 border border-gray-300" />
                )}
              </div>
            </div>
          </div>
        </div>

        <div>
          <div className="border border-black p-3 text-xs">
            <div className="space-y-1">
              <div className="flex justify-between border-b border-gray-300 py-1">
                <span>Taxable Amount</span>
                <span>₹ {formatCurrency(data.subtotal).replace('R$', '')}</span>
              </div>
              <div className="flex justify-between border-b border-gray-300 py-1">
                <span>Total CGST/SGST @</span>
                <span>{data.taxRate}%</span>
                <span>₹ {formatCurrency(data.taxAmount).replace('R$', '')}</span>
              </div>
              <div className="flex justify-between border-b border-gray-300 py-1">
                <span>Total IGST Amount</span>
                <span>₹ 0.00</span>
              </div>
              <div className="flex justify-between border-b border-gray-300 py-1">
                <span>Total GST Amount</span>
                <span>₹ {formatCurrency(data.taxAmount).replace('R$', '')}</span>
              </div>
              <div className="flex justify-between font-bold py-1">
                <span>Round Off</span>
                <span>0.44</span>
              </div>
              <div className="flex justify-between font-bold text-base border-t-2 border-black pt-2 mt-2">
                <span>Total Bill Amount</span>
                <span>₹ {formatCurrency(data.total).replace('R$', '')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Amount in Words */}
      <div className="mb-4 border border-black p-2">
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <p><span className="font-bold">Total GST (in Words):</span> Eight Lakh Three Thousand Three Hundred Thirty Eight & Fifty Six Paise Only</p>
          </div>
          <div>
            <p><span className="font-bold">Total Amount (in Words):</span> Fifty Two Lakh Sixty Five Thousand Three Hundred Thirty One Only</p>
          </div>
        </div>
      </div>

      {/* Transport Details */}
      <div className="mb-4 border border-black p-2">
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <p><span className="font-bold">Narration 2:</span> HAVELLS - UP 9945119800</p>
          </div>
          <div>
            <p><span className="font-bold">Transporter Name:</span> LEAP'S N LAP'S LOGISTT</p>
            <p><span className="font-bold">Mode of Transport:</span> By Road</p>
            <p><span className="font-bold">Vehicle No.:</span> HR7489746</p>
          </div>
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="mb-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-xs">
            <h4 className="font-bold mb-2">TERMS & CONDITIONS :</h4>
            <div className="space-y-1">
              <p>1. All Disputes are subject to Delhi Jurisdiction only.</p>
              <p>2. Our responsibility ceases as soon as the goods leave our premises.</p>
              <p>3. If payment is not paid within Seven days 24 % interest will be charged.</p>
              <p>4. Our responsibility ceases, as soon as we have hand over the material to the Courier or Buyer.</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-xs mb-8">For {data.company.name}</p>
            <div className="border-t border-black pt-2 mt-16">
              <p className="text-xs font-bold">Authorised Signatory</p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-xs text-gray-500 border-t border-gray-300 pt-2">
        <p>Generated by Billify Generator BR • {formatDate(new Date())}</p>
      </div>
    </div>
  );
}
