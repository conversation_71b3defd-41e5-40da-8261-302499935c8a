import React, { useState, useEffect } from 'react';
import { ArrowLeft, Download, Eye, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useInvoice } from '../context/InvoiceContext';
import { generatePDF } from '../utils/pdf';
import { ModernTemplate } from './templates/ModernTemplate';
import { ProfessionalTemplate } from './templates/ProfessionalTemplate';
import { MinimalTemplate } from './templates/MinimalTemplate';
import { ClassicTemplate } from './templates/ClassicTemplate';
import { CorporateTemplate } from './templates/CorporateTemplate';
import { TaxInvoiceTemplate } from './templates/TaxInvoiceTemplate';

const templates = [
  { id: 'modern', name: 'Moderno', component: ModernTemplate },
  { id: 'professional', name: 'Profissional', component: ProfessionalTemplate },
  { id: 'minimal', name: 'Minimalista', component: MinimalTemplate },
  { id: 'classic', name: 'Clássico', component: ClassicTemplate },
  { id: 'corporate', name: 'Corporativo', component: CorporateTemplate },
  { id: 'taxinvoice', name: 'Tax Invoice', component: TaxInvoiceTemplate },
];

export function TemplatePage() {
  const { state, dispatch } = useInvoice();
  const navigate = useNavigate();
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(state.data.template);

  const currentTemplate = templates.find(t => t.id === selectedTemplate);
  const TemplateComponent = currentTemplate?.component || ModernTemplate;

  useEffect(() => {
    dispatch({ type: 'UPDATE_TEMPLATE', payload: selectedTemplate });
  }, [selectedTemplate, dispatch]);

  const handleGeneratePDF = async () => {
    setIsGeneratingPDF(true);
    try {
      await generatePDF(state.data);
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      alert('Erro ao gerar PDF. Tente novamente.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleBackToForm = () => {
    navigate('/');
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleBackToForm}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Voltar ao Formulário</span>
            </button>
            <div className="h-6 w-px bg-gray-300" />
            <h1 className="text-2xl font-bold text-gray-900">Visualização da Fatura</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              {templates.map(template => (
                <option key={template.id} value={template.id}>
                  Template {template.name}
                </option>
              ))}
            </select>
            
            <button
              onClick={handleGeneratePDF}
              disabled={isGeneratingPDF}
              className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-6 py-3 rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGeneratingPDF ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Gerando PDF...</span>
                </>
              ) : (
                <>
                  <Download className="w-4 h-4" />
                  <span>Gerar PDF</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Template Preview */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="max-w-4xl mx-auto">
          <div id="invoice-preview" className="bg-white">
            <TemplateComponent data={state.data} />
          </div>
        </div>
      </div>

      {/* Template Selector */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <Eye className="w-5 h-5 mr-3 text-purple-600" />
          Escolher Template
        </h2>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {templates.map((template) => (
            <div
              key={template.id}
              onClick={() => setSelectedTemplate(template.id)}
              className={`cursor-pointer p-4 rounded-lg border-2 transition-all duration-300 hover:scale-105 ${
                selectedTemplate === template.id
                  ? 'border-purple-500 bg-purple-50 shadow-lg'
                  : 'border-gray-200 hover:border-purple-300'
              }`}
            >
              <div className={`w-full h-24 rounded-lg mb-3 flex items-center justify-center ${
                selectedTemplate === template.id ? 'bg-purple-100' : 'bg-gray-100'
              }`}>
                <Eye className={`w-8 h-8 ${
                  selectedTemplate === template.id ? 'text-purple-600' : 'text-gray-400'
                }`} />
              </div>
              <h3 className="font-medium text-sm text-gray-900 text-center">{template.name}</h3>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}