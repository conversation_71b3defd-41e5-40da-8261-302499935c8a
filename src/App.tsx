import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { InvoiceProvider } from './context/InvoiceContext';
import { Layout } from './components/Layout';
import { InvoiceCreator } from './components/InvoiceCreator';
import { TemplatePage } from './components/TemplatePage';
import { PixDebugger } from './components/PixDebugger';

function App() {
  const isDevelopment = import.meta.env.DEV;

  return (
    <InvoiceProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<InvoiceCreator />} />
            <Route path="/template" element={<TemplatePage />} />
          </Routes>
        </Layout>
        {/* Debugger PIX - apenas em desenvolvimento */}
        {isDevelopment && <PixDebugger />}
      </Router>
    </InvoiceProvider>
  );
}

export default App;