import QRCode from 'qrcode';
import { PixInfo } from '../types/invoice';

// Interface para dados decodificados do PIX
export interface DecodedPixData {
  payloadFormatIndicator?: string;
  merchantAccountInfo?: {
    gui?: string;
    key?: string;
    description?: string;
    url?: string;
  };
  merchantCategoryCode?: string;
  transactionCurrency?: string;
  transactionAmount?: string;
  countryCode?: string;
  merchantName?: string;
  merchantCity?: string;
  postalCode?: string;
  additionalDataField?: {
    billNumber?: string;
    mobileNumber?: string;
    storeLabel?: string;
    loyaltyNumber?: string;
    referenceLabel?: string;
    customerLabel?: string;
    terminalLabel?: string;
    purposeTransaction?: string;
    additionalConsumerDataRequest?: string;
  };
  crc16?: string;
  unreservedTemplates?: { [key: string]: any };
}

// Função para decodificar payload PIX
export function decodePixPayload(payload: string): DecodedPixData {
  const result: DecodedPixData = {};
  let index = 0;

  while (index < payload.length - 4) { // -4 para o CRC no final
    const id = payload.substring(index, index + 2);
    const length = parseInt(payload.substring(index + 2, index + 4), 10);
    const value = payload.substring(index + 4, index + 4 + length);

    index += 4 + length;

    switch (id) {
      case '00':
        result.payloadFormatIndicator = value;
        break;
      case '26':
        result.merchantAccountInfo = parseSubFields(value);
        break;
      case '52':
        result.merchantCategoryCode = value;
        break;
      case '53':
        result.transactionCurrency = value;
        break;
      case '54':
        result.transactionAmount = value;
        break;
      case '58':
        result.countryCode = value;
        break;
      case '59':
        result.merchantName = value;
        break;
      case '60':
        result.merchantCity = value;
        break;
      case '61':
        result.postalCode = value;
        break;
      case '62':
        result.additionalDataField = parseAdditionalDataField(value);
        break;
      default:
        // Templates não reservados (27-51)
        if (parseInt(id) >= 27 && parseInt(id) <= 51) {
          if (!result.unreservedTemplates) result.unreservedTemplates = {};
          result.unreservedTemplates[id] = parseSubFields(value);
        }
        break;
    }
  }

  // CRC16 está nos últimos 4 caracteres
  if (payload.length >= 4) {
    result.crc16 = payload.substring(payload.length - 4);
  }

  return result;
}

function parseSubFields(data: string): any {
  const result: any = {};
  let index = 0;

  while (index < data.length) {
    const id = data.substring(index, index + 2);
    const length = parseInt(data.substring(index + 2, index + 4), 10);
    const value = data.substring(index + 4, index + 4 + length);

    index += 4 + length;

    switch (id) {
      case '00':
        result.gui = value;
        break;
      case '01':
        result.key = value;
        break;
      case '02':
        result.description = value;
        break;
      case '25':
        result.url = value;
        break;
      default:
        result[id] = value;
        break;
    }
  }

  return result;
}

function parseAdditionalDataField(data: string): any {
  const result: any = {};
  let index = 0;

  while (index < data.length) {
    const id = data.substring(index, index + 2);
    const length = parseInt(data.substring(index + 2, index + 4), 10);
    const value = data.substring(index + 4, index + 4 + length);

    index += 4 + length;

    switch (id) {
      case '01':
        result.billNumber = value;
        break;
      case '02':
        result.mobileNumber = value;
        break;
      case '03':
        result.storeLabel = value;
        break;
      case '04':
        result.loyaltyNumber = value;
        break;
      case '05':
        result.referenceLabel = value;
        break;
      case '06':
        result.customerLabel = value;
        break;
      case '07':
        result.terminalLabel = value;
        break;
      case '08':
        result.purposeTransaction = value;
        break;
      case '09':
        result.additionalConsumerDataRequest = value;
        break;
      default:
        result[id] = value;
        break;
    }
  }

  return result;
}

// Função para testar com dados do Santander fornecido
export function testSantanderPixPayload(): void {
  console.log('=== TESTE COMPARATIVO COM SANTANDER ===');

  // Dados do QR Code do Santander fornecido
  const santanderData = {
    valor: 2590.00,
    nome: 'WELLINGTON FERREIRA DE OLIVEIRA',
    chave: '283.047.438-41',
    cpf: '047.438',
    cidade: 'Unknown', // Não especificado na imagem
    informacoes: 'PIX teste'
  };

  console.log('Dados do Santander:', santanderData);

  // Gerar payload com os mesmos dados - versão simples
  const testPixInfo: PixInfo = {
    key: '28304743841', // CPF sem formatação
    name: 'WELLINGTON FERREIRA DE OLIVEIRA',
    city: 'SAO PAULO', // Assumindo uma cidade
    amount: 2590.00
  };

  console.log('\n--- PAYLOAD SIMPLES ---');
  const payloadSimple = generatePixPayload(testPixInfo);
  console.log('Payload simples:', payloadSimple);
  console.log('Comprimento:', payloadSimple.length);

  const validationSimple = validatePixPayload(payloadSimple);
  console.log('Validação simples:', validationSimple.isValid ? '✅ Válido' : '❌ Inválido');
  if (!validationSimple.isValid) {
    console.log('Erros:', validationSimple.errors);
  }

  console.log('\n--- PAYLOAD AVANÇADO ---');
  const payloadAdvanced = generateAdvancedPixPayload(testPixInfo);
  console.log('Payload avançado:', payloadAdvanced);
  console.log('Comprimento:', payloadAdvanced.length);

  const validationAdvanced = validatePixPayload(payloadAdvanced);
  console.log('Validação avançada:', validationAdvanced.isValid ? '✅ Válido' : '❌ Inválido');
  if (!validationAdvanced.isValid) {
    console.log('Erros:', validationAdvanced.errors);
  }

  const decoded = decodePixPayload(payloadSimple);
  console.log('\nPayload decodificado (simples):', JSON.stringify(decoded, null, 2));
  console.log('========================================');
}

// Função para testar a geração do payload PIX
export function testPixPayload(): void {
  const testPixInfo: PixInfo = {
    key: '11999999999', // CPF ou telefone de teste
    name: 'TESTE USUARIO',
    city: 'SAO PAULO',
    amount: 100.50
  };

  const payload = generatePixPayload(testPixInfo);
  console.log('=== TESTE PIX PAYLOAD ===');
  console.log('Dados de entrada:', testPixInfo);
  console.log('Payload gerado:', payload);
  console.log('Comprimento:', payload.length);

  const validation = validatePixPayload(payload);
  console.log('Validação:', validation);

  const decoded = decodePixPayload(payload);
  console.log('Payload decodificado:', decoded);
  console.log('========================');
}

// Função para analisar um payload PIX de banco
export function analyzePixPayload(payload: string): void {
  console.log('=== ANÁLISE DE PAYLOAD PIX ===');
  console.log('Payload:', payload);
  console.log('Comprimento:', payload.length);

  const decoded = decodePixPayload(payload);
  console.log('Decodificado:', JSON.stringify(decoded, null, 2));

  const validation = validatePixPayload(payload);
  console.log('Validação:', validation);
  console.log('==============================');
}

// Versão simplificada mais compatível com bancos
export function generatePixPayload(pixInfo: PixInfo): string {
  // Função auxiliar para formatar campo EMV
  const formatField = (id: string, value: string): string => {
    const length = value.length.toString().padStart(2, '0');
    return `${id}${length}${value}`;
  };

  // Função auxiliar para formatar subcampos
  const formatSubField = (id: string, value: string): string => {
    const length = value.length.toString().padStart(2, '0');
    return `${id}${length}${value}`;
  };

  // 00 - Payload Format Indicator (obrigatório)
  const payloadFormatIndicator = formatField('00', '01');

  // 26 - Merchant Account Information (PIX) - Campo obrigatório
  const gui = formatSubField('00', 'br.gov.bcb.pix');
  const pixKey = formatSubField('01', pixInfo.key);

  const merchantAccountInfo = gui + pixKey;
  const merchantAccountInformation = formatField('26', merchantAccountInfo);

  // 52 - Merchant Category Code (obrigatório)
  const merchantCategoryCode = formatField('52', '0000');

  // 53 - Transaction Currency (obrigatório) - 986 = BRL
  const transactionCurrency = formatField('53', '986');

  // 54 - Transaction Amount (obrigatório para PIX com valor)
  const transactionAmount = pixInfo.amount.toFixed(2);
  const transactionAmountField = formatField('54', transactionAmount);

  // 58 - Country Code (obrigatório)
  const countryCode = formatField('58', 'BR');

  // 59 - Merchant Name (obrigatório, máximo 25 caracteres)
  const merchantName = pixInfo.name.substring(0, 25).toUpperCase();
  const merchantNameField = formatField('59', merchantName);

  // 60 - Merchant City (obrigatório, máximo 15 caracteres)
  const merchantCity = pixInfo.city.substring(0, 15).toUpperCase();
  const merchantCityField = formatField('60', merchantCity);

  // Montar payload sem CRC na ordem correta (sem campos opcionais)
  let payload = payloadFormatIndicator +
                merchantAccountInformation +
                merchantCategoryCode +
                transactionCurrency +
                transactionAmountField +
                countryCode +
                merchantNameField +
                merchantCityField +
                '6304'; // CRC16 placeholder

  // Calcular CRC16
  const crc = calculateCRC16(payload);
  payload += crc;

  return payload;
}

// Versão avançada com mais campos (para testes)
export function generateAdvancedPixPayload(pixInfo: PixInfo): string {
  // Função auxiliar para formatar campo EMV
  const formatField = (id: string, value: string): string => {
    const length = value.length.toString().padStart(2, '0');
    return `${id}${length}${value}`;
  };

  // Função auxiliar para formatar subcampos
  const formatSubField = (id: string, value: string): string => {
    const length = value.length.toString().padStart(2, '0');
    return `${id}${length}${value}`;
  };

  // 00 - Payload Format Indicator (obrigatório)
  const payloadFormatIndicator = formatField('00', '01');

  // 01 - Point of Initiation Method (opcional)
  const pointOfInitiation = formatField('01', '12'); // 12 = static QR code

  // 26 - Merchant Account Information (PIX) - Campo obrigatório
  const gui = formatSubField('00', 'br.gov.bcb.pix');
  const pixKey = formatSubField('01', pixInfo.key);
  const description = formatSubField('02', 'PIX teste');

  const merchantAccountInfo = gui + pixKey + description;
  const merchantAccountInformation = formatField('26', merchantAccountInfo);

  // 52 - Merchant Category Code (obrigatório)
  const merchantCategoryCode = formatField('52', '0000');

  // 53 - Transaction Currency (obrigatório) - 986 = BRL
  const transactionCurrency = formatField('53', '986');

  // 54 - Transaction Amount (obrigatório para PIX com valor)
  const transactionAmount = pixInfo.amount.toFixed(2);
  const transactionAmountField = formatField('54', transactionAmount);

  // 58 - Country Code (obrigatório)
  const countryCode = formatField('58', 'BR');

  // 59 - Merchant Name (obrigatório, máximo 25 caracteres)
  const merchantName = pixInfo.name.substring(0, 25).toUpperCase();
  const merchantNameField = formatField('59', merchantName);

  // 60 - Merchant City (obrigatório, máximo 15 caracteres)
  const merchantCity = pixInfo.city.substring(0, 15).toUpperCase();
  const merchantCityField = formatField('60', merchantCity);

  // 62 - Additional Data Field Template (opcional)
  const billNumber = formatSubField('01', 'PIX001');
  const additionalDataField = formatField('62', billNumber);

  // Montar payload sem CRC na ordem correta
  let payload = payloadFormatIndicator +
                pointOfInitiation +
                merchantAccountInformation +
                merchantCategoryCode +
                transactionCurrency +
                transactionAmountField +
                countryCode +
                merchantNameField +
                merchantCityField +
                additionalDataField +
                '6304'; // CRC16 placeholder

  // Calcular CRC16
  const crc = calculateCRC16(payload);
  payload += crc;

  return payload;
}

function calculateCRC16(payload: string): string {
  // CRC16-CCITT (0x1021) conforme especificação EMV para PIX
  // Polinômio: x^16 + x^12 + x^5 + 1 (0x1021)
  // Valor inicial: 0xFFFF
  // XOR final: 0x0000
  // Reflexão: Não

  let crc = 0xFFFF;

  for (let i = 0; i < payload.length; i++) {
    const byte = payload.charCodeAt(i);
    crc ^= (byte << 8);

    for (let bit = 0; bit < 8; bit++) {
      if (crc & 0x8000) {
        crc = ((crc << 1) ^ 0x1021) & 0xFFFF;
      } else {
        crc = (crc << 1) & 0xFFFF;
      }
    }
  }

  // Converter para hexadecimal maiúsculo com 4 dígitos
  return crc.toString(16).toUpperCase().padStart(4, '0');
}

// Função para validar payload PIX conforme padrão EMV
export function validatePixPayload(payload: string): { isValid: boolean; errors: string[]; details?: DecodedPixData } {
  const errors: string[] = [];

  try {
    // Decodificar o payload para análise detalhada
    const decoded = decodePixPayload(payload);

    // 1. Verificar Payload Format Indicator (campo 00) - obrigatório
    if (!decoded.payloadFormatIndicator || decoded.payloadFormatIndicator !== '01') {
      errors.push('Campo 00 (Payload Format Indicator) deve ser "01"');
    }

    // 2. Verificar Merchant Account Information (campo 26) - obrigatório para PIX
    if (!decoded.merchantAccountInfo) {
      errors.push('Campo 26 (Merchant Account Information) é obrigatório');
    } else {
      // Verificar GUI do PIX
      if (!decoded.merchantAccountInfo.gui || decoded.merchantAccountInfo.gui !== 'br.gov.bcb.pix') {
        errors.push('GUI do PIX deve ser "br.gov.bcb.pix"');
      }

      // Verificar se tem chave PIX
      if (!decoded.merchantAccountInfo.key) {
        errors.push('Chave PIX é obrigatória no campo 26');
      }
    }

    // 3. Verificar Merchant Category Code (campo 52) - obrigatório
    if (!decoded.merchantCategoryCode) {
      errors.push('Campo 52 (Merchant Category Code) é obrigatório');
    }

    // 4. Verificar Transaction Currency (campo 53) - obrigatório
    if (!decoded.transactionCurrency || decoded.transactionCurrency !== '986') {
      errors.push('Campo 53 (Transaction Currency) deve ser "986" (BRL)');
    }

    // 5. Verificar Country Code (campo 58) - obrigatório
    if (!decoded.countryCode || decoded.countryCode !== 'BR') {
      errors.push('Campo 58 (Country Code) deve ser "BR"');
    }

    // 6. Verificar Merchant Name (campo 59) - obrigatório
    if (!decoded.merchantName || decoded.merchantName.length === 0) {
      errors.push('Campo 59 (Merchant Name) é obrigatório');
    } else if (decoded.merchantName.length > 25) {
      errors.push('Campo 59 (Merchant Name) deve ter no máximo 25 caracteres');
    }

    // 7. Verificar Merchant City (campo 60) - obrigatório
    if (!decoded.merchantCity || decoded.merchantCity.length === 0) {
      errors.push('Campo 60 (Merchant City) é obrigatório');
    } else if (decoded.merchantCity.length > 15) {
      errors.push('Campo 60 (Merchant City) deve ter no máximo 15 caracteres');
    }

    // 8. Verificar CRC16 (campo 63) - obrigatório
    if (!decoded.crc16 || decoded.crc16.length !== 4) {
      errors.push('CRC16 deve ter exatamente 4 caracteres hexadecimais');
    } else {
      // Verificar se o CRC está correto
      const payloadWithoutCrc = payload.substring(0, payload.length - 4);
      const calculatedCrc = calculateCRC16(payloadWithoutCrc + '6304');
      if (decoded.crc16 !== calculatedCrc) {
        errors.push(`CRC16 inválido. Esperado: ${calculatedCrc}, Encontrado: ${decoded.crc16}`);
      }
    }

    // 9. Verificar comprimento mínimo
    if (payload.length < 50) {
      errors.push('Payload muito curto (mínimo 50 caracteres)');
    }

    // 10. Verificar se o payload contém apenas caracteres válidos
    if (!/^[0-9A-Za-z\.\-\@\+\s]*$/.test(payload)) {
      errors.push('Payload contém caracteres inválidos');
    }

    return {
      isValid: errors.length === 0,
      errors,
      details: decoded
    };

  } catch (error) {
    errors.push(`Erro ao decodificar payload: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    return {
      isValid: false,
      errors
    };
  }
}

export async function generatePixQRCode(pixInfo: PixInfo): Promise<string> {
  console.log('=== GERANDO QR CODE PIX ===');
  console.log('Dados de entrada:', pixInfo);

  // Validar dados de entrada
  if (!pixInfo.key || !pixInfo.name || !pixInfo.city || pixInfo.amount <= 0) {
    throw new Error('Dados PIX incompletos ou inválidos');
  }

  const payload = generatePixPayload(pixInfo);
  console.log('Payload gerado:', payload);
  console.log('Comprimento do payload:', payload.length);

  // Decodificar e analisar o payload gerado
  const decoded = decodePixPayload(payload);
  console.log('Payload decodificado:', JSON.stringify(decoded, null, 2));

  // Validar payload
  const validation = validatePixPayload(payload);
  console.log('Resultado da validação:', {
    isValid: validation.isValid,
    errors: validation.errors
  });

  if (!validation.isValid) {
    console.error('❌ Payload PIX inválido:', validation.errors);
    throw new Error(`Payload PIX inválido: ${validation.errors.join(', ')}`);
  } else {
    console.log('✅ Payload PIX válido');
  }

  try {
    const qrCodeUrl = await QRCode.toDataURL(payload, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
      width: 256,
    });

    console.log('✅ QR Code gerado com sucesso');
    console.log('Payload final para teste:', payload);
    console.log('===============================');

    return qrCodeUrl;
  } catch (error) {
    console.error('❌ Erro ao gerar QR Code:', error);
    console.log('===============================');
    throw new Error(`Erro ao gerar QR Code PIX: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}