import QRCode from 'qrcode';
import { PixInfo } from '../types/invoice';

// Função para testar a geração do payload PIX
export function testPixPayload(): void {
  const testPixInfo: PixInfo = {
    key: '11999999999', // CPF ou telefone de teste
    name: 'TESTE USUARIO',
    city: 'SAO PAULO',
    amount: 100.50
  };

  const payload = generatePixPayload(testPixInfo);
  console.log('=== TESTE PIX PAYLOAD ===');
  console.log('Dados de entrada:', testPixInfo);
  console.log('Payload gerado:', payload);
  console.log('Comprimento:', payload.length);

  const validation = validatePixPayload(payload);
  console.log('Validação:', validation);
  console.log('========================');
}

export function generatePixPayload(pixInfo: PixInfo): string {
  // Função auxiliar para formatar campo
  const formatField = (id: string, value: string): string => {
    return `${id}${String(value.length).padStart(2, '0')}${value}`;
  };

  // 00 - Payload Format Indicator
  const payloadFormatIndicator = formatField('00', '01');

  // 26 - Merchant Account Information (PIX)
  const gui = formatField('00', 'br.gov.bcb.pix');
  const pixKey = formatField('01', pixInfo.key);
  const merchantAccountInfo = gui + pixKey;
  const merchantAccountInformation = formatField('26', merchantAccountInfo);

  // 52 - Merchant Category Code
  const merchantCategoryCode = formatField('52', '0000');

  // 53 - Transaction Currency (986 = BRL)
  const transactionCurrency = formatField('53', '986');

  // 54 - Transaction Amount
  const transactionAmount = pixInfo.amount.toFixed(2);
  const transactionAmountField = formatField('54', transactionAmount);

  // 58 - Country Code
  const countryCode = formatField('58', 'BR');

  // 59 - Merchant Name (máximo 25 caracteres)
  const merchantName = pixInfo.name.substring(0, 25).toUpperCase();
  const merchantNameField = formatField('59', merchantName);

  // 60 - Merchant City (máximo 15 caracteres)
  const merchantCity = pixInfo.city.substring(0, 15).toUpperCase();
  const merchantCityField = formatField('60', merchantCity);

  // Montar payload sem CRC
  let payload = payloadFormatIndicator +
                merchantAccountInformation +
                merchantCategoryCode +
                transactionCurrency +
                transactionAmountField +
                countryCode +
                merchantNameField +
                merchantCityField +
                '6304'; // CRC16 placeholder

  // Calculate CRC16
  const crc = calculateCRC16(payload);
  payload += crc;

  return payload;
}

function calculateCRC16(payload: string): string {
  // CRC16-CCITT (0x1021) conforme especificação do BACEN
  let crc = 0xFFFF;

  for (let i = 0; i < payload.length; i++) {
    const byte = payload.charCodeAt(i);
    crc ^= (byte << 8);

    for (let j = 0; j < 8; j++) {
      if (crc & 0x8000) {
        crc = ((crc << 1) ^ 0x1021) & 0xFFFF;
      } else {
        crc = (crc << 1) & 0xFFFF;
      }
    }
  }

  return crc.toString(16).toUpperCase().padStart(4, '0');
}

// Função para validar e debugar o payload PIX
export function validatePixPayload(payload: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Verificar se começa com 000201 (Payload Format Indicator)
  if (!payload.startsWith('000201')) {
    errors.push('Payload deve começar com 000201');
  }

  // Verificar se contém campo 26 (Merchant Account Information)
  if (!payload.includes('26')) {
    errors.push('Campo 26 (Merchant Account Information) não encontrado');
  }

  // Verificar se contém br.gov.bcb.pix
  if (!payload.includes('br.gov.bcb.pix')) {
    errors.push('GUI do PIX não encontrado');
  }

  // Verificar se termina com CRC de 4 dígitos
  const crcMatch = payload.match(/6304([A-F0-9]{4})$/);
  if (!crcMatch) {
    errors.push('CRC16 inválido ou não encontrado');
  }

  // Verificar comprimento mínimo
  if (payload.length < 50) {
    errors.push('Payload muito curto');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export async function generatePixQRCode(pixInfo: PixInfo): Promise<string> {
  console.log('=== GERANDO QR CODE PIX ===');
  console.log('Dados de entrada:', pixInfo);

  const payload = generatePixPayload(pixInfo);
  console.log('Payload gerado:', payload);
  console.log('Comprimento do payload:', payload.length);

  // Quebrar o payload em partes para debug
  console.log('Análise do payload:');
  console.log('- Payload Format (00):', payload.substring(0, 6));
  console.log('- Merchant Account (26):', payload.substring(6, payload.indexOf('5204')));
  console.log('- Category Code (52):', payload.substring(payload.indexOf('5204'), payload.indexOf('5204') + 8));
  console.log('- Currency (53):', payload.substring(payload.indexOf('5303'), payload.indexOf('5303') + 9));

  // Debug: validar payload
  const validation = validatePixPayload(payload);
  console.log('Validação:', validation);

  if (!validation.isValid) {
    console.error('❌ Payload PIX inválido:', validation.errors);
  } else {
    console.log('✅ Payload PIX válido');
  }

  try {
    const qrCodeUrl = await QRCode.toDataURL(payload, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    });

    console.log('✅ QR Code gerado com sucesso');
    console.log('===============================');

    return qrCodeUrl;
  } catch (error) {
    console.error('❌ Erro ao gerar QR Code:', error);
    console.log('===============================');
    throw new Error('Erro ao gerar QR Code PIX');
  }
}