# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Development Server**
```bash
npm run dev
```

**Build Project**
```bash
npm run build
```

**Lint Code**
```bash
npm run lint
```

**Preview Production Build**
```bash
npm run preview
```

## Project Architecture

This is a React + TypeScript bill/invoice generator application built with Vite, using Context API for state management and featuring multiple customizable templates.

### Key Architecture Components

**State Management**: Centralized through `InvoiceContext` (src/context/InvoiceContext.tsx) using React's useReducer hook. The context manages:
- Multi-step invoice creation workflow (details → parties → shipping → items → preview)
- Invoice data persistence to localStorage with auto-save
- Automatic total calculations when items change

**Core Data Types**: Defined in `src/types/invoice.ts`:
- `InvoiceData`: Main invoice structure containing all invoice information
- `InvoiceStep`: Type-safe step navigation ('details' | 'parties' | 'shipping' | 'items' | 'preview')
- Company/Client info, items, shipping, and PIX payment integration

**Template System**: Located in `src/components/templates/` with 5 different invoice templates:
- Each template is a React component accepting `InvoiceData` prop
- Templates include: Modern, Professional, Minimal, Classic, Corporate
- All templates exported through `src/components/templates/index.ts`

**Multi-Step Workflow**: Step components in `src/components/steps/`:
- DetailsStep: Invoice metadata (number, dates, currency)
- PartiesStep: Company and client information
- ShippingStep: Shipping address and costs
- ItemsStep: Invoice line items with automatic calculations
- PreviewStep: Final review with template selection and PDF generation

**PDF Generation**: Uses html2canvas + jsPDF (src/utils/pdf.ts) to convert React components to PDF exports.

**PIX Integration**: Brazilian instant payment system integration (src/utils/pix.ts) for QR code generation.

### Project Structure

```
src/
├── components/
│   ├── steps/          # Multi-step form components
│   ├── templates/      # Invoice templates
│   └── [core components]
├── context/            # React Context providers
├── types/              # TypeScript type definitions  
├── utils/              # Utility functions (PDF, PIX, invoice logic)
└── App.tsx            # Main app with routing
```

### Technology Stack

- **Framework**: React 18 + TypeScript + Vite
- **Styling**: TailwindCSS + PostCSS
- **State**: React Context + useReducer
- **Routing**: React Router DOM
- **Forms**: React Hook Form + Zod validation
- **PDF Export**: html2canvas + jsPDF
- **Icons**: Lucide React
- **PIX**: Custom QR code generation

### Development Notes

- Invoice data auto-saves to localStorage every 1 second
- Templates are hot-swappable in preview step
- All monetary calculations happen automatically when items change
- Brazilian market focused (CPF/CNPJ, PIX payments, BRL currency)
- No test framework currently configured